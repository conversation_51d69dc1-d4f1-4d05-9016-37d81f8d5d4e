import { cn } from "@/utils/utils";
export const AthenaComponent = ({ className, ...props }) => (
  <svg
    width="300"
    className={cn("dark:invert", className)}
    height="239"
    viewBox="0 0 300 239"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M275.757 193.713L300 234.575H275.757H251.515L275.757 193.713Z"
      fill="#CDCDCD"
    />
    <path
      d="M39.7102 193.713L63.9528 234.575H15.4676L39.7102 193.713Z"
      fill="#CDCDCD"
    />
    <rect
      x="39.7432"
      y="193.713"
      width="236.049"
      height="40.8621"
      fill="#CDCDCD"
    />
    <rect
      x="58.9604"
      y="97.006"
      width="37.4384"
      height="89.8965"
      fill="#CDCDCD"
    />
    <rect
      x="112.872"
      y="97.006"
      width="37.4384"
      height="89.8965"
      fill="#CDCDCD"
    />
    <rect
      x="166.783"
      y="97.006"
      width="37.4384"
      height="89.8965"
      fill="#CDCDCD"
    />
    <rect
      x="220.695"
      y="97.006"
      width="37.4384"
      height="89.8965"
      fill="#CDCDCD"
    />
    <rect
      x="34.5449"
      y="62.4897"
      width="245.628"
      height="24.7484"
      fill="#CDCDCD"
    />
    <rect
      x="34.5449"
      y="62.6381"
      width="137.972"
      height="27.8419"
      transform="rotate(-27 34.5449 62.6381)"
      fill="#CDCDCD"
    />
    <rect
      x="157.315"
      width="137.972"
      height="27.8419"
      transform="rotate(27 157.315 0)"
      fill="#CDCDCD"
    />
    <path
      d="M260.29 197.425L284.532 238.287H260.29H236.047L260.29 197.425Z"
      fill="#242424"
    />
    <path
      d="M24.2426 197.425L48.4852 238.287H4.61771e-05L24.2426 197.425Z"
      fill="#242424"
    />
    <rect
      x="24.2749"
      y="197.425"
      width="236.049"
      height="40.8621"
      fill="#242424"
    />
    <rect
      x="43.4932"
      y="100.718"
      width="37.4384"
      height="89.8965"
      fill="#242424"
    />
    <rect
      x="97.4047"
      y="100.718"
      width="37.4384"
      height="89.8965"
      fill="#242424"
    />
    <rect
      x="151.316"
      y="100.718"
      width="37.4384"
      height="89.8965"
      fill="#242424"
    />
    <rect
      x="205.227"
      y="100.718"
      width="37.4384"
      height="89.8965"
      fill="#242424"
    />
    <rect
      x="19.0771"
      y="66.2019"
      width="245.628"
      height="24.7484"
      fill="#242424"
    />
    <rect
      x="19.0771"
      y="66.3504"
      width="137.972"
      height="27.8419"
      transform="rotate(-27 19.0771 66.3504)"
      fill="#242424"
    />
    <rect
      x="141.847"
      y="3.71234"
      width="137.972"
      height="27.8419"
      transform="rotate(27 141.847 3.71234)"
      fill="#242424"
    />
  </svg>
);
